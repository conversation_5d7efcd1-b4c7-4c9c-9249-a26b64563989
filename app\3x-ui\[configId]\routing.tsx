import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIXrayConfig } from '@/panels/3x-ui/utils';
import { smartFetch } from '@/lib/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, Save, RotateCcw, GripVertical, Database } from 'lucide-react-native';
import React, { useState, useEffect, useCallback } from 'react';
import { SafeAreaView, StyleSheet, View, Alert, TouchableOpacity } from 'react-native';
import DragList, { DragListRenderItemInfo } from 'react-native-draglist';

// 路由规则类型定义（不再使用id，直接用索引）
interface RouteRule {
  domainMatcher?: 'hybrid' | 'linear';
  type: 'field';
  domain?: string[];
  ip?: string[];
  port?: string;
  sourcePort?: string;
  network?: 'tcp' | 'udp' | 'tcp,udp';
  source?: string[];
  user?: string[];
  inboundTag?: string[];
  protocol?: ('http' | 'tls' | 'quic' | 'bittorrent')[];
  attrs?: Record<string, string>;
  outboundTag?: string;
  balancerTag?: string;
  ruleTag?: string;
}

// Geo文件更新进度类型
interface GeoUpdateProgress {
  fileName: string;
  completed: boolean;
  success?: boolean;
  error?: string;
}

// Geo文件更新结果类型
interface GeoUpdateResult {
  totalFiles: number;
  completedFiles: number;
  successFiles: number;
  failedFiles: number;
  results: GeoUpdateProgress[];
}

export default function RoutingScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig, setServerConfig } = useAppStore();

  const [hasChanges, setHasChanges] = useState(false);
  // 虚拟顺序状态：存储规则原始索引的排序数组
  const [virtualOrder, setVirtualOrder] = useState<string[]>([]);

  // Geo更新相关状态
  const [isUpdatingGeo, setIsUpdatingGeo] = useState(false);
  const [geoUpdateProgress, setGeoUpdateProgress] = useState<GeoUpdateProgress[]>([]);
  const [geoUpdatePercentage, setGeoUpdatePercentage] = useState(0);

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;
  const serverConfig = getServerConfig(configId || '');

  // 直接从全局状态读取路由规则
  const rules = (serverConfig?.xray?.routing?.rules as RouteRule[]) || [];

  // 根据虚拟顺序获取排序后的规则数组（根据原始索引）
  const getOrderedRules = useCallback(() => {
    return virtualOrder
      .map((originalIndex) => rules[parseInt(originalIndex, 10)])
      .filter((rule) => rule !== undefined) as RouteRule[];
  }, [virtualOrder, rules]);

  // 加载xray配置
  const loadXrayConfig = useCallback(async () => {
    if (!currentConfig) return;

    try {
      await getThreeXUIXrayConfig(currentConfig);
      // 数据已经在getThreeXUIXrayConfig中存储到serverConfig了，这里不需要额外操作
    } catch (error) {
      console.error('Load xray config failed:', error);
      Alert.alert('错误', '加载路由配置失败');
    }
  }, [currentConfig]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadXrayConfig();
    }, [loadXrayConfig])
  );

  // 加载路由规则
  useEffect(() => {
    if (serverConfig?.xray?.routing?.rules) {
      const rawRules = serverConfig.xray.routing.rules as RouteRule[];
      const currentRulesCount = rawRules.length;
      const previousRulesCount = virtualOrder.length;

      // 如果规则个数发生变化，说明添加了新规则
      if (currentRulesCount > previousRulesCount) {
        // 在虚拟索引顺序中加入新的index
        const newIndexes: string[] = [];
        for (let i = previousRulesCount; i < currentRulesCount; i++) {
          newIndexes.push(i.toString());
        }
        setVirtualOrder(prev => [...prev, ...newIndexes]);
        // 将已修改设为true
        setHasChanges(true);
      } else if (currentRulesCount < previousRulesCount) {
        // 如果规则减少了，需要调整虚拟顺序
        setVirtualOrder(rawRules.map((_, index) => index.toString()));
        setHasChanges(false);
      } else if (virtualOrder.length === 0) {
        // 初始化虚拟顺序为原始索引顺序
        setVirtualOrder(rawRules.map((_, index) => index.toString()));
        setHasChanges(false);
      }
    } else {
      setVirtualOrder([]);
      setHasChanges(false);
    }
  }, [serverConfig, virtualOrder.length]);

  // 添加规则
  const handleAddRule = () => {
    router.push({
      pathname: '/3x-ui/rule-config',
      params: { configId }
    });
  };

  // 编辑规则 - 根据虚拟顺序找到原始索引
  const handleEditRule = (virtualIndex: number) => {
    // 获取虚拟位置对应的原始索引
    const originalIndex = virtualOrder[virtualIndex];

    router.push({
      pathname: '/3x-ui/rule-config',
      params: { configId, ruleIndex: originalIndex }
    });
  };

  // 拖拽重排序 - 只修改虚拟顺序
  const handleReorder = async (fromIndex: number, toIndex: number) => {
    const newVirtualOrder = [...virtualOrder];
    const movedRuleId = newVirtualOrder.splice(fromIndex, 1)[0];
    newVirtualOrder.splice(toIndex, 0, movedRuleId);
    setVirtualOrder(newVirtualOrder);
    setHasChanges(true);
  };

  // 保存规则
  const handleSave = () => {
    if (!currentConfig) {
      Alert.alert('错误', '未找到配置信息');
      return;
    }

    const updatedServerConfig = { ...serverConfig };
    if (!updatedServerConfig.xray) updatedServerConfig.xray = {};
    if (!updatedServerConfig.xray.routing) updatedServerConfig.xray.routing = {};

    // 按照虚拟顺序重新排列规则（直接使用原始索引）
    const reorderedRules = virtualOrder
      .map((idxStr) => rules[parseInt(idxStr, 10)])
      .filter((rule) => rule !== undefined) as RouteRule[];

    updatedServerConfig.xray.routing.rules = reorderedRules;
    setServerConfig(configId || '', updatedServerConfig);
    setHasChanges(false);

    Alert.alert('成功', '路由规则已保存');
  };

  // 重启服务
  const handleRestart = () => {
    Alert.alert(
      '确认重启',
      '确定要重启Xray服务吗？这将应用新的路由规则配置。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重启',
          onPress: () => {
            // TODO: 实现重启逻辑
            Alert.alert('提示', '重启功能暂未实现');
          }
        }
      ]
    );
  };

  // 更新单个geo文件
  const updateSingleGeoFile = async (fileName: string): Promise<GeoUpdateProgress> => {
    try {
      if (!currentConfig) {
        throw new Error('配置信息不存在');
      }

      const baseUrl = `${currentConfig.protocol}://${currentConfig.url}`;
      const updateUrl = `${baseUrl}/server/updateGeofile/${fileName}`;

      const requestOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      };

      const response = await smartFetch(updateUrl, requestOptions, currentConfig);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      return {
        fileName,
        completed: true,
        success: result.success === true,
        error: result.success ? undefined : result.msg || '更新失败'
      };
    } catch (error) {
      console.error(`Update ${fileName} failed:`, error);
      return {
        fileName,
        completed: true,
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  // 更新geo数据库
  const handleUpdateGeo = () => {
    Alert.alert(
      '确认更新',
      '确定要更新geo数据库吗？这将下载最新的geosite和geoip数据。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '更新',
          onPress: updateGeoFiles
        }
      ]
    );
  };

  // 执行geo文件更新
  const updateGeoFiles = async () => {
    const geoFiles = [
      'geosite.dat',
      'geoip.dat',
      'geosite_IR.dat',
      'geoip_IR.dat',
      'geosite_RU.dat',
      'geoip_RU.dat'
    ];

    setIsUpdatingGeo(true);
    setGeoUpdateProgress([]);
    setGeoUpdatePercentage(0);

    const results: GeoUpdateProgress[] = [];

    try {
      for (let i = 0; i < geoFiles.length; i++) {
        const fileName = geoFiles[i];

        // 更新进度
        const currentProgress = Math.round(((i) / geoFiles.length) * 100);
        setGeoUpdatePercentage(currentProgress);

        // 更新单个文件
        const result = await updateSingleGeoFile(fileName);
        results.push(result);

        // 更新进度状态
        setGeoUpdateProgress([...results]);
      }

      // 完成所有更新
      setGeoUpdatePercentage(100);

      // 显示结果
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;

      let message = `更新完成！\n成功: ${successCount} 个文件\n失败: ${failedCount} 个文件`;

      if (failedCount > 0) {
        const failedFiles = results.filter(r => !r.success);
        message += '\n\n失败详情:\n' + failedFiles.map(f => `${f.fileName}: ${f.error}`).join('\n');
      }

      Alert.alert(
        failedCount === 0 ? '更新成功' : '更新完成',
        message,
        [{ text: '确定' }]
      );

    } catch (error) {
      console.error('Geo update failed:', error);
      Alert.alert('更新失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsUpdatingGeo(false);
      setGeoUpdateProgress([]);
      setGeoUpdatePercentage(0);
    }
  };

  // 渲染规则项
  const renderRuleItem = ({ item, onDragStart, onDragEnd, index }: DragListRenderItemInfo<RouteRule>) => {
    const rule = item;

    const getRuleDescription = (rule: RouteRule): string => {
      const parts: string[] = [];

      if (rule.domain && rule.domain.length > 0) {
        parts.push(`域名: ${rule.domain.slice(0, 2).join(', ')}${rule.domain.length > 2 ? '...' : ''}`);
      }
      if (rule.ip && rule.ip.length > 0) {
        parts.push(`IP: ${rule.ip.slice(0, 2).join(', ')}${rule.ip.length > 2 ? '...' : ''}`);
      }
      if (rule.port) {
        parts.push(`端口: ${rule.port}`);
      }
      if (rule.network) {
        parts.push(`网络: ${rule.network}`);
      }
      if (rule.protocol) {
        parts.push(`协议: ${rule.protocol}`);
      }
      if (rule.inboundTag) {
        parts.push(`入站: ${rule.inboundTag}`);
      }

      return parts.length > 0 ? parts.join(' | ') : '空规则';
    };

    return (
      <View style={styles.cardContainer}>
        <TouchableOpacity
          style={styles.card}
          onPress={() => handleEditRule(index)}
          onPressIn={onDragStart}
          onPressOut={onDragEnd}
        >
          <View style={styles.cardHeader}>
            <Text style={[styles.title, { color: textColor }]}>
              {rule.ruleTag || `规则 ${parseInt(virtualOrder[index]) + 1}`}
            </Text>
            <View style={styles.rightSection}>
              {rule.outboundTag && (
                <Badge style={styles.outboundBadge}>
                  <Text style={styles.badgeText}>{rule.outboundTag}</Text>
                </Badge>
              )}
              <GripVertical size={20} color={textColor + '60'} />
            </View>
          </View>
          <Text style={[styles.ruleDescription, { color: textColor + '80' }]}>
            {getRuleDescription(rule)}
          </Text>
        </TouchableOpacity>
        <View style={[styles.divider, { backgroundColor: borderColor }]} />
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 固定在顶部的按钮组 */}
      <View style={[styles.headerContainer, { backgroundColor }]}>
        <View style={styles.header}>
          <View style={styles.buttonGroup}>
            <Button
              variant="secondary"
              size="sm"
              onPress={handleAddRule}
              style={styles.button}
            >
              <Plus size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>添加</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleSave}
              disabled={!hasChanges}
              style={[styles.button, !hasChanges && styles.disabledButton]}
            >
              <Save size={16} color={hasChanges ? textColor : textColor + '60'} />
              <Text style={[styles.buttonText, { color: hasChanges ? textColor : textColor + '60' }]}>保存</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleRestart}
              style={styles.button}
            >
              <RotateCcw size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>重启</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleUpdateGeo}
              disabled={isUpdatingGeo}
              style={[styles.button, isUpdatingGeo && styles.disabledButton]}
            >
              <Database size={16} color={isUpdatingGeo ? textColor + '60' : textColor} />
              <Text style={[styles.buttonText, { color: isUpdatingGeo ? textColor + '60' : textColor }]}>
                {isUpdatingGeo ? `更新中 ${geoUpdatePercentage}%` : '更新geo'}
              </Text>
            </Button>
          </View>
        </View>
        <View style={[styles.headerDivider, { backgroundColor: borderColor }]} />
      </View>



      {/* 规则列表 */}
      {rules.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: textColor }]}>
            暂无路由规则
          </Text>
          <Text style={[styles.emptySubtitle, { color: textColor + '80' }]}>
            点击上方添加按钮创建第一条路由规则
          </Text>
        </View>
      ) : (
        <DragList
          data={getOrderedRules()}
          renderItem={renderRuleItem}
          keyExtractor={(_: RouteRule, index: number) => `rule-${virtualOrder[index]}`}
          contentContainerStyle={styles.listContent}
          onReordered={handleReorder}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    // 固定在顶部的容器
  },
  header: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  headerDivider: {
    height: 1,
  },
  list: {
    flex: 1,
  },
  cardContainer: {
    backgroundColor:'white'
  },
  card: {
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  divider: {
    height: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  listContent: {
    paddingTop: 0,
    height:'100%'
  },
  ruleDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 4,
  },
  outboundBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
});
